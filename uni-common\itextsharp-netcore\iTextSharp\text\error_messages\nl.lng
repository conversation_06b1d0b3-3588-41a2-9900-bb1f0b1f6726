﻿# Nederlandse vertaling van de foutboodschappen
1.2.does.not.contain.an.usable.cmap={1} {2} bevat geen bruikbare cmap.
1.2.is.not.a.ttf.font.file={1} {2} is geen TTF font file.
1.at.file.pointer.2={1} bij file pointer {2}
1.bit.samples.are.not.supported.for.horizontal.differencing.predictor={1}-bit samples worden niet ondersteund voor een horizontaal differencierende Predictor.
1.cannot.be.embedded.due.to.licensing.restrictions={1} kan niet ingebed worden omwille van licentie restricties.
1.component.s.is.not.supported=geen ondersteuning voor {1} component(en)
1.corrupted.jfif.marker={1} is een corrupte JFIF marker.
1.is.an.unknown.graphics.state.dictionary={1} is een onbekende graphics state dictionary
1.is.an.unknown.image.format={1} is een onbekend Image formaat.
1.is.not.a.true.type.file={1} is geen true type file.
1.is.not.a.ttf.otf.or.ttc.font.file={1} is geen TTF, OTF of TTC font file.
1.is.not.a.valid.jpeg.file={1} is geen geldig JPEG bestand.
1.is.not.a.valid.name.for.checkbox.appearance={1} is geen geldige naam voor een checkbox appearance (moet Off of Yes zijn)
1.is.not.a.valid.number.2={1} is geen geldig nummer - {2}
1.is.not.a.valid.page.size.format.2={1} is geen geldig pagina formaat: {2}
1.is.not.a.valid.placeable.windows.metafile={1} is geen geldige plaatsbaar WMF bestand.
1.is.not.a.valid.ttc.file={1} is geen geldige TTC file.
1.is.not.a.valid.ttf.file={1} is geen geldig TTF bestand.
1.is.not.a.valid.ttf.or.otf.file={1} is geen geldige TTF of OTF file.
1.is.not.an.acceptable.value.for.the.field.2={1} is geen geldige waarde voor het veld {2}
1.is.not.an.afm.or.pfm.font.file={1} is geen AFM of PFM font file.
1.method.can.be.only.used.in.mergeFields.mode.please.use.addDocument={1} methode kan alleen gebruikt worden in mergeFields mode, gebruik addDocument.
1.method.cannot.be.used.in.mergeFields.mode.please.use.addDocument={1} method kan niet gebruikt worden in mergeFields mode, gebruikt addDocument.
1.must.have.8.bits.per.component={1} moet 8 bits per component bevatten.
1.near.line.2.column.3={1} in lijn {2}, kolom {3}
1.not.found.as.file.or.resource={1} niet gevonden als bron of bronbestand.
1.not.found.as.resource={1} niet gevonden als een bron(bestand).
1.unsupported.jpeg.marker.2={1}: niet ondersteunde JPEG marker: {2}
1.value.of.intent.key.is.not.allowed={1} waarde van Intent sleutel is niet toegelaten.
1.value.of.ri.key.is.not.allowed={1} waarde van RI sleutel is niet toegelaten.
a.form.xobject.dictionary.shall.not.contain.ps.key=Een form XObject dictionary mag geen PS sleutel bevatten.
a.form.xobject.dictionary.shall.not.contain.opi.key=Een form XObject dictionary mag geen OPI sleutel bevatten.
a.group.object.with.an.s.key.with.a.value.of.transparency.shall.not.be.included.in.a.form.xobject=Een Group object met een S sleutel met een Transparency waarde mag niet in een form XOBject zitten.
a.pattern.can.not.be.used.as.a.template.to.create.an.image=Een patroon kan niet gebruikt worden als template om een afbeelding te maken.
a.pdfa.file.may.have.only.one.pdfa.outputintent=Een PDF/A conform document mag slechts 1 PDF/A OutputIntent bevatten.
a.pdfx.conforming.document.cannot.be.encrypted=Een PDF/X document mag niet versleuteld worden.
a.signature.image.should.be.present.when.rendering.mode.is.graphic.only=Een signature image is vereist als de rendering mode graphic only is.
a.signature.image.should.be.present.when.rendering.mode.is.graphic.and.description=A signature image is vereist als rendering mode graphic and description.
a.string.1.was.passed.in.state.only.on.off.and.toggle.are.allowed=Een string '{1}' werd gebruikt als status. Alleen 'ON', 'OFF' en 'Toggle' zijn toegelaten.
a.tab.position.may.not.be.lower.than.0.yours.is.1=Een tab positie mag niet kleiner zijn dan 0; jouw tab positie is {1}
a.table.should.have.at.least.1.column=Een tabel moet uit ten minste 1 kolom bestaan.
a.tiling.or.shading.pattern.cannot.be.used.as.a.color.space.in.a.shading.pattern=Een tiling of shading pattern kan niet gebruikt worden als een color space in een shading pattern
a.title.is.not.a.layer=Een titel is geen layer
addcell.cell.has.null.value=addCell - cell bevat een null-waarde
addcell.error.in.reserve=addCell - fout in reserve
addcell.illegal.column.argument=addCell - ongeldig kolom argument
addcell.null.argument=addCell - argument null
addcell.only.cells.or.tables.allowed=addCell - alleen cellen en tabellen zijn toegelaten
addcell.point.has.null.value=addCell - point bevat een null-waarde
adding.a.cell.at.the.location.1.2.with.a.colspan.of.3.and.a.rowspan.of.4.is.illegal.beyond.boundaries.overlapping=Een cel toevoegen op positie ({1},{2}) met colspan {3} en rowspan {4} is niet mogelijk (overlapt of gaat buiten bereik).
add.image.exception=Exception bij toevoegen van afbeelding {1}
afrelationship.value.shall.be.alternative=AFRelationship waarde moet Alternative zijn.
ai.not.found.1=AI niet gevonden: ({1})
ai.too.short.1=AI niet lang genoeg: ({1})
all.colour.channels.in.the.jpeg2000.data.shall.have.the.same.bit-depth=Alle colour channels in de JPEG2000 data moeten dezelfde bit-depth hebben.
all.fill.bits.preceding.eol.code.must.be.0=Alle fill bits voorafgaand aan EOL code moeten 0 zijn.
all.halftones.shall.have.halftonetype.1.or.5=Alle halftones in een conform PDF/A document moeten waarde 1 of 5 hebben voor de HalftoneType sleutel.
all.the.fonts.must.be.embedded.this.one.isn.t.1=Alle fonts moeten ingebed worden. Deze font is niet ingebed: {1}
already.attempted.a.read.on.this.jbig2.file=read() was al geprobeerd op dit Jbig2 bestand
alt.entry.should.specify.alternate.description.for.1.element=Alt veld moet een alternatieve beschrijving voor {1} element specifi�ren.
an.annotation.dictionary.shall.contain.the.f.key=Een annotation dictionary moet de F sleutel bevatten.
an.annotation.dictionary.shall.not.contain.the.ca.key.with.a.value.other.than.1=Een annotation dictionary mag geen CA sleutel bevatten met een waarde verschillend van 1.0.
an.appearance.was.requested.without.a.variable.text.field=Er werd een appearance gevraagd zonder variabel text field.
an.extgstate.dictionary.shall.not.contain.the.tr.key=Een ExtGState dictionary mag geen TR sleutel bevatten.
an.extgstate.dictionary.shall.not.contain.the.htp.key=Een ExtGState dictionary mag geen HTP sleutel bevatten.
an.extgstate.dictionary.shall.not.contain.the.halftonename.key=Een ExtGState dictionary mag geen HalftoneName sleutel bevatten.
an.extgstate.dictionary.shall.contain.the.halftonetype.key.of.value.1.or.5=Een ExtGState dictionary moet de HalftoneType sleutel met waarde 1 of 5 bevatten.
an.extgstate.dictionary.shall.not.contain.the.TR2.key.with.a.value.other.than.default=Een ExtGState dictionary mag geen TR2 sleutel bevatten met een waarde verschillend van Default.
an.image.dictionary.shall.not.contain.alternates.key=Een Image dictionary mag geen Alternates sleutel bevatten.
an.image.dictionary.shall.not.contain.opi.key=Een Image dictionary mag geen OPI sleutel bevatten.
an.image.mask.cannot.contain.another.image.mask=Een image mask mag geen ander image mask bevatten.
an.uncolored.pattern.was.expected=Er werd een ongekleurd patroon verwacht.
an.uncolored.tile.pattern.can.not.have.another.pattern.or.shading.as.color=Een ongekleurd tile pattern kan geen ander pattern of shading als color gebruiken.
annotation.of.type.1.should.have.contents.key=Annotation van type {1} moet een Contents sleutel hebben.
annotation.type.1.not.allowed=Annotation type {1} niet toegelaten.
annotation.type.not.supported.flattening=Dit annotatie type is niet ondersteund tijdens het flattenen. Deze wordt overgeslaan.
appearance.dictionary.of.widget.subtype.and.btn.field.type.shall.contain.only.the.n.key.with.dictionary.value=Appearance dictionary van subtype Widget en field type Btn mag enkel de N sleutel met als waarde een Dictionaty bevatten
appearance.dictionary.shall.contain.only.the.n.key.with.stream.value=Appearance dictionary mag enkel de N sleutel met een stream waarde bevatten.
append.mode.does.not.support.changing.the.encryption.status=Append mode laat geen wijziging toe van de encryptie status.
append.mode.requires.a.document.without.errors.even.if.recovery.was.possible=Append mode vergt een document zonder fouten, zelfs als recovery mogelijk was.
authenticated.attribute.is.missing.the.digest=Er ontbreekt een geauthenticeerd attribuut in de digest.
b.type.before.end.of.paragraph.at.index.1=B type voor het einde van een paragraaf op index: {1}
bad.certificate.and.key=Verkeerd certificaat en sleutel.
bad.endianness.tag.not.0x4949.or.0x4d4d=Verkeerde endianness tag (niet 0x4949 of 0x4d4d).
bad.linebreak.1.at.index.2=slechte lijnafbreking: {1} op index: {2}
bad.magic.number.should.be.42=Verkeerd 'magic number', zou 42 moeten zijn.
bad.user.password=Verkeerd user password
badly.formated.directory.string=Slecht geformateerde directory string
badly.formed.ucc.string.1=Slecht gevormde UCC string: {1}
base64.input.not.properly.padded=Base64 input is niet correct 'gepad'.
bbox.must.be.a.4.element.array=BBox moet een array met 4 elementen zijn.
bits.per.component.must.be.1.2.4.or.8=Bits-per-component moet 1, 2, 4, of 8 zijn.
bits.per.sample.1.is.not.supported=Bits per sample {1} wordt niet ondersteund.
blend.mode.1.not.allowed=Blend mode {1} niet toegelaten.
bookmark.end.tag.out.of.place=Bookmark end tag op verkeerde plaats.
both.colors.must.be.of.the.same.type=Beide kleuren moeten van het zelfde type zijn.
buffersize.1=bufferlengte {1}
can.only.push.back.one.byte=Werkt enkel met ��n byte ineens.
can.t.add.1.to.multicolumntext.with.complex.columns={1} kan niet toegevoegd worden aan een MultiColumnText met complexe kolommen
can.t.decode.pkcs7signeddata.object=Kan het PKCS7SignedData object niet decoderen
can.t.find.page.size.1=Kan de pagina afmetingen niet vinden {1}
can.t.find.signing.certificate.with.serial.1=Kan het signing certificate met serienummer {1} niet vinden
can.t.read.document.structure=Kan de documentstructuur niet lezen
cannot.change.destination.of.external.link=De destination van een external link kan niet gewijzigd worden
cannot.handle.box.sizes.higher.than.2.32=Een box size groter dan 2^32 is niet mogelijk
cf.not.found.encryption=/CF niet gevonden (encryption)
codabar.must.have.at.least.a.start.and.stop.character=Codabar moet ten minste een start en stop karakter bevatten.
codabar.must.have.one.of.abcd.as.start.stop.character=Codabar moet een van 'ABCD' als start/stop karakter hebben.
color.value.outside.range.0.255=Color value buiten bereik 0-255.
color.not.found=Kleur '{1}' niet gevonden.
colors.are.not.allowed.in.uncolored.tile.patterns=Kleuren zijn niet toegelaten in ongekleurde patronen.
colorspace.calrgb.is.not.allowed=Colorspace CalRGB is niet toegelaten.
colorspace.rgb.is.not.allowed=Colorspace RGB is niet toegelaten.
column.coordinate.of.location.must.be.gt.eq.0.and.lt.nr.of.columns=Kolom coordinaat moet groter dan of gelijk zijn aan 0 en kleiner dan het aantal kolommen
columntext.go.with.simulate.eq.eq.false.and.text.eq.eq.null=ColumnText.go met simulate==false en text==null.
components.must.be.1.3.or.4=Componenten moeten 1, 3, of 4 zijn.
compression.jpeg.is.only.supported.with.a.single.strip.this.image.has.1.strips=Compressie JPEG wordt enkel ondersteund voor ��n enkele strip. Jouw JPEG heeft {1} strips.
conflict.in.classmap=Conflict in ClassMap {1}.
conflict.in.rolemap=Conflict in RoleMap {1}.
content.can.not.be.added.to.a.pdfimportedpage=Er kan geen extra content toegevoegd worden aan een PdfImportedPage.
content.was.already.written.to.the.output=Er werd al inhoud naar de output gestuurd.
corrupted.png.file=Corrupt PNG bestand.
could.not.find.web.browser=Kon geen webbrowser vinden.
could.not.flatten.file.untagged.annotations.found=Kan het document niet flattenen: ongetagde annotations gevonden!
count.of.referred.to.segments.had.bad.value.in.header.for.segment.1.starting.at.2=Een aantal referred-to segmenten heeft een ongeldige waarde in de header voor segment {1} beginnend bij {2}
creating.page.stamp.not.allowed.for.tagged.reader=PageStamp is niet toegelaten voor tagged reader.
crypt.filter.is.not.permitted.inline.image=Crypt filter is niet toegelaten voor inline images.
defaultcryptfilter.not.found.encryption=/DefaultCryptFilter niet gevonden (encryption)
deprecated.setstate.and.noop.actions.are.not.allowed=Deprecated set-state en no-op actions zijn niet toegelaten.
destination.end.tag.out.of.place=Bestemming end tag op verkeerde plaats.
destoutputprofile.in.the.pdfa1.outputintent.dictionary.shall.be.rgb=DestOutputProfile in de PDF/A-1 OutputIntent dictionary moet RGB zijn.
devicecmyk.may.be.used.only.if.the.file.has.a.cmyk.pdfa.outputIntent=DeviceCMYK mag enkel gebruikt worden als het document een PDF/A OutputIntent heeft die een CMYK colour space gebruikt.
devicecmyk.shall.only.be.used.if.defaultcmyk.pdfa.or.outputintent=DeviceCMYK mag enkel gebruikt worden als een device-onafhankelijke DefaultCMYK colour space ingesteld is, of als een DeviceN-gebaseerde DefaultCMYK colour space ingesteld is wanneer de DeviceCMYK colour space gebruikt wordt of het document een PDF/A OutputIntent heeft dat een CMYK destination profiel bevat.
devicegray.may.be.used.only.if.the.file.has.a.rgb.or.cmyk.pdfa.outputIntent=DeviceGray mag enkel gebruikt worden als het document een PDF/A OutputIntent heeft die een RGB of CMYK colour space gebruikt.
devicegray.shall.only.be.used.if.defaultgray.pdfa.or.outputintent=DeviceGray mag enkel gebruikt worden als een device-onafhankelijke DefaultGray colour space ingesteld is wanneer de DeviceGray colour space gebruikt wordt, of als een PDF/A OutputIntent aanwezig is.
devicergb.and.devicecmyk.colorspaces.cannot.be.used.both.in.one.file=DeviceRGB en DeviceCMYK colorspaces mogen niet samen in 1 file gebruikt worden.
devicergb.may.be.used.only.if.the.file.has.a.rgb.pdfa.outputIntent=DeviceRGB mag enkel gebruikt worden als het document een PDF/A OutputIntent heeft die een RGB colour space gebruikt.
devicergb.shall.only.be.used.if.defaultrgb.pdfa.or.outputintent=DeviceRGB mag enkel gebruikt worden als een device-onafhankelijke DefaultRGB colour space ingesteld is wanneer de DeviceRGB colour space gebruikt wordt, of als het document een PDF/A OutputIntent heeft die een RGB destination profiel bevat.
devicen.color.shall.have.the.same.number.of.colorants.as.the.destination.DeviceN.color.space=DeviceN kleur moet hetzelfde aantal colorants hebben als de destination color space
devicen.component.names.shall.be.different=DeviceN componentnamen moeten onderling allemaal verschillend zijn, op de naam None na
dictionary.key.1.is.not.a.name=De sleutel van de Dictionary {1} is geen naam.
dictionary.key.is.not.a.name=De sleutel voor de Dictionary is geen naam.
different.pdf.a.version=Verschillende PDF/A versie.
dimensions.of.a.cell.are.attributed.automagically.see.the.faq=De afmetingen van een Cell worden automatisch toegekend. Zie de FAQ.
dimensions.of.a.cell.can.t.be.calculated.see.the.faq=De afmetingen van een Cell kunnen niet berekend worden. Zie de FAQ.
dimensions.of.a.table.can.t.be.calculated.see.the.faq=Dimensies van een Table object kunnen niet berekend worden. Zie de FAQ.
directory.number.too.large=Directory nummer te groot.
document.1.has.already.been.added=Het Document {1} is als gesloten (preclosed).
document.already.pre.closed=Het Document is als gesloten (preclosed).
document.catalog.dictionary.shall.include.a.markinfo.dictionary.whose.entry.marked.shall.have.a.value.of.true=Document catalog dictionary moet een MarkInfo dictionary bevatten waarvan het Marked veld de waarde true moet hebben.
document.catalog.dictionary.should.contain.lang.entry=Document catalog dictionary mag geen Lang veld bevatten.
document.fields.cannot.be.copied.in.tagged.mode=Document fields kunnen niet gekopieerd worden in tagged mode.
ef.key.of.file.specification.dictionary.shall.contain.dictionary.with.valid.f.key=De EF sleutel van de file specification dictionary voor een embedded file moet een dictionary bevatten met een geldige F sleutel.
element.not.allowed=Element niet toegelaten.
embedded.file.shall.contain.valid.params.key=Een embedded file moet een geldige Params sleutel hebben.
embedded.file.shall.contain.params.key.with.valid.moddate.key=Een embedded file moet een Params sleutel hebben met een geldige ModDate sleutel.
embedded.file.shall.contain.pdf.mime.type=Een embedded file moet een correct pdf mime type hebben.
embedded.files.are.not.permitted=Embedded files zijn niet toegelaten.
encryption.can.only.be.added.before.opening.the.document=Encryptie kan enkel bepaald worden voor het document geopend wordt.
eol.code.word.encountered.in.black.run=EOL code woord gevonden in Black run.
eol.code.word.encountered.in.white.run=EOL code woord gevonden in White run.
error.attempting.to.launch.web.browser=Fout bij het opstarten van de webbrowser.
error.expected.hex.character.and.not.char.thenextbyte.1=Fout: hexademicaal karakter verwacht in plaats van (char)theNextByte:{1}
error.expected.the.end.of.a.dictionary=Fout: einde van een dictionary verwacht.
error.in.attribute.processing=Fout bij verwerking attribuut
error.in.base64.code.reading.stream=Fout in de Base64 code reading stream.
error.parsing.cmap.beginbfchar.expected.cosstring.or.cosname.and.not.1=Fout bij het parsen van CMap beginbfchar, {COSString or COSName} verwacht in plaats van {1}
error.reading.objstm=Fout tijdens het lezen van ObjStm
error.reading.string=Fout bij het lezen van een string
error.resolving.freetext.font=Kan het lettertype van annotatie niet oplossen. Het wordt niet afgedrukt
error.with.jp.marker=Foute JP Marker
every.annotation.shall.have.at.least.one.appearance.dictionary=Elke annotation moet ten minste 1 appearance dictionary hebben
exactly.one.colour.space.specification.shall.have.the.value.0x01.in.the.approx.field=Exact 1 colour space specificatie moet de waarde 0x01 in het APPROX veld hebben.
expected.ftyp.marker=FTYP Marker verwacht
expected.gt.for.tag.lt.1.gt=Karakter > verwacht voor tag: <{1}/>
expected.ihdr.marker=IHDR Marker verwacht
expected.colr.marker=COLR Marker verwacht
expected.jp.marker=JP Marker verwacht
expected.jp2h.marker=JP2H Marker verwacht
extra.samples.are.not.supported=Extra samples worden niet ondersteund.
failed.to.get.tsa.response.from.1=Slaagde er niet in een TSA antwoord te krijgen van '{1}'
fdf.header.not.found=FDF header niet gevonden.
field.flattening.is.not.supported.in.append.mode=Field flattening is niet ondersteund in append mode.
field.names.cannot.contain.a.dot=De naam van een veld mag geen punt bevatten.
file.header.flags.bits.2.7.not.0=file header vlaggen bits 2 tot 7 zijn niet 0
file.header.idstring.not.good.at.byte.1=file header idstring is niet correct op byte {1}
file.is.not.a.valid.png=Het bestand is geen geldige PNG afbeelding.
file.position.0.cross.reference.entry.in.this.xref.subsection=Bestands positie 0 voor cross-reference entry in deze xref subsectie
file.specification.dictionary.shall.contain.correct.afrelationship.key=De file specification dictionary voor een embedded file moet een correct AFRelationship sleutel bevatten.
file.specification.dictionary.shall.contain.f.uf.and.desc.entries=De file specification dictionary voor een embedded file moet de F en UF sleutels bevatten en zou de Desc sleuten moeten bevatten.
filter.ccittfaxdecode.is.only.supported.for.images=Filter CCITTFaxDecode is enkel ondersteund voor foto's
first.scanline.must.be.1d.encoded=Eerste scanline moet 1D geencodeerd zijn.
font.1.with.2.encoding.is.not.a.cjk.font=Font '{1}' met encoding '{2}' is geen CJK font.
font.1.with.2.is.not.recognized=Font '{1}' met '{2}' werd niet herkend.
font.and.size.must.be.set.before.writing.any.text=Font en size moeten bepaald zijn vooraleer je tekst schrijft.
font.size.too.small.1=Font size te klein: {1}
fontfactoryimp.cannot.be.null=FontFactoryImp kan niet null zijn.
freetext.annotation.doesnt.contain.da=FreeText Annotatie bevat geen DA. Deze annotatie kan niet worden geflattened en wordt overgeslaan.
freetext.flattening.is.not.supported.in.append.mode=FreeText flattening is niet ondersteund in append mode.
annotation.flattening.is.not.supported.in.append.mode=Het flattenen van annotations is niet ondersteund in append mode.
getcell.at.illegal.index.1.max.is.2=getCell op ongeldige index:{1} maximum: {2}
getcell.at.illegal.index.1=getCell op ongeldige index : {1}
gif.signature.nor.found=Gif signature niet gevonden.
graphics.state.stack.depth.is.greater.than.28=Graphics state stackdiepte is groter dan 28.
greaterthan.not.expected='>' op een onverwachte plaats
halftones.shall.not.contains.halftonename=Halftones in een conform PDF/A-2 document mogen geen HalftoneName sleutel bevatten.
handler.1.already.registered=XObject handler {1} reeds geregistreerd
if.device.rgb.cmyk.gray.used.in.file.that.file.shall.contain.pdfa.outputintent=Als een ongecalibreerde colour space(DeviceRGB/DeviceCMYK/DeviceGray) gebruikt wordt in een document, dan moet dat document een PDF/A OutputIntent bevatten.
if.outputintents.array.more.than.one.entry.the.same.indirect.object=Als de OutputIntents array van een document meer dan 1 entry bevat, dan moeten alle entries die een DestOutputProfile sleutel bevatten hetzelfde indirect object hebben als waarde voor die sleutel.
if.the.document.not.contain.outputintent.transparencygroup.shall.comtain.cs.key=Als het document geen PDF/A OutputIntent bevat, dan moeten alle Page objecten die transparantie bevatten de Group sleuten bevatten, en de attribute dictionary die de waarde vormt van die Group key moet een CS entry bevatten waarvan de waarde zal gebruikt worden als de default blending colour space.
illegal.capacity.1=Ongeldige capaciteit: {1}
illegal.character.in.ascii85decode=Ongeldig karakter in ASCII85Decode.
illegal.character.in.asciihexdecode=Ongeldig karakter in ASCIIHexDecode.
illegal.length.in.ascii85decode=Ongeldige lengte in ASCII85Decode.
illegal.length.value=Ongeldige waarde voor de lengte.
illegal.load.1=Ongeldige load: {1}
illegal.p.value=Ongeldige waarde voor P.
illegal.paragraph.embedding.level.1=ongeldig embedding niveau paragraaf: {1}
illegal.r.value=Ongeldige waarde voor R.
illegal.type.value.at.1.2=ongeldige waarde voor type op {1}: {2}
illegal.v.value=Ongeldige waarde voor V.
illegal.value.for.predictor.in.tiff.file=Ongeldige waarde voor Predictor in TIFF file.
illegal.ve.value=Ongeldige VE waarde.
illegal.pages.tree=Illegal pages tree.
illegal.resources.tree=Illegal Resources Tree.
improperly.padded.base64.input=Ongeldig 'gepadde' Base64 input.
in.a.page.label.the.page.numbers.must.be.greater.or.equal.to.1=In een page label moeten de pagina nummers groter dan of gelijk aan 1 zijn.
in.codabar.start.stop.characters.are.only.allowed.at.the.extremes=In codabar worden start/stop karakters enkel toegelaten bij begin en einde van de string.
incompatible.pdf.a.conformance.level=Incompatibele PDF/A conformance level.
incomplete.palette=onvolledig palet
inconsistent.mapping=Inconsistente mapping.
inconsistent.writers.are.you.mixing.two.documents=Inconsistente writers. Ben je twee documenten aan het verhaspelen?
incorrect.segment.type.in.1=Foutief segment type in {1}
infinite.table.loop=Oneindige tabel lus; rij inhoud is groter dan de pagina (bijvoorbeeld omdat een foto niet geschaald is.).
inline.elements.with.role.null.are.not.allowed=De tagging mode markInlineElementsOnly wordt gebruikt. Inline elementen met rol 'null' zijn niet toegelaten.
insertion.of.illegal.element.1=Invoeging van een ongeldig Element: {1}
inserttable.point.has.null.value=insertTable - punt bevat een null-waarde
inserttable.table.has.null.value=insertTable - tabel bevat een null-waarde
inserttable.wrong.columnposition.1.of.location.max.eq.2=insertTable -- verkeerde komom positie ({1}); maximum ={2}
insufficient.data=Involdoende data.
insufficient.length=Onvoldoende lengte.
internal.inconsistence=Interne inconsistentie.
inthashtableiterator=IntHashtableIterator
invalid.additional.action.type.1=Ongeldig 'additional action' type: {1}
invalid.additional.action.type.1=Ongeldig 'additional action' type: {1}
invalid.ai.length.1=Ongeldige AI lengte: ({1})
invalid.border.style=Ongeldige border style.
invalid.character.in.base64.data=Ongeldig karakter in de Base64 data.
invalid.code.encountered.while.decoding.2d.group.3.compressed.data=Ongeldige code gevonden tijdens het decoderen van 2D group 3 gecomprimeerde data.
invalid.code.encountered.while.decoding.2d.group.4.compressed.data=Ongeldige code gevonden tijdens het decoderen van 2D group 4 gecomprimeerde data.
invalid.code.encountered=Ongeldige code gevonden.
invalid.code.type=Ongeldig code type.
invalid.codeword.size=Ongeldige codeword lengte.
invalid.color.type=Ongeldig kleurtype.
invalid.cross.reference.entry.in.this.xref.subsection=Ongeldige cross-reference entry in deze xref subsectie
invalid.end.tag.1=Ongeldige end tag - {1}
invalid.generation.number=Ongeldig generation nummer.
invalid.http.response.1=Ongeldig HTTP antwoord: {1}
invalid.icc.profile=Ongeldig ICC profiel.
invalid.index.1=Ongeldige index: {1}
invalid.listtype.value=Ongeldige waarde voor listType.
invalid.magic.value.for.bmp.file=Ongeldige 'magic value' voor een BMP bestand.
invalid.named.action=Ongeldige named action.
invalid.object.number=Ongeldig object nummer.
invalid.page.additional.action.type.1=Ongeldig 'page additional action' type: {1}
invalid.page.number.1=Ongeldig pagina nummer: {1}
invalid.run.direction.1=Ongeldige run direction: {1}
invalid.status.1=Ongeldige status: {1}
invalid.tsa.1.response.code.2=Ongeldig TSA '{1}' antwoord, code {2}
invalid.type.was.passed.in.state.1=Ongeldig type gebruikt als state: {1}
invalid.use.of.a.pattern.a.template.was.expected=Ongeldig gebruik van een patroon. Er werd een template verwacht.
irregular.columns.are.not.supported.in.composite.mode=Onregelmatige kolommen zijn niet toegelaten in composite mode.
it.is.not.possible.to.free.reader.in.merge.fields.mode=freeReader is niet mogelijk in mergeFields mode.
java.awt.image.fetch.aborted.or.errored=Ophalen van java.awt.Image afgebroken of misgelopen.
java.awt.image.interrupted.waiting.for.pixels=java.awt.Image onderbroken; aan het wachten op pixels!
jpeg2000.enumerated.colour.space.19.(CIEJab).shall.not.be.used=JPEG2000 enumerated colour space 19 (CIEJab) mag niet gebruikt worden.
key.is.null=sleutel is nul.
keyword.encrypt.shall.not.be.used.in.the.trailer.dictionary=Keyword Encrypt mag niet gebruikt worden in de trailer dictionary.
lab.cs.black.point=De BlackPoint entry in Lab color space mag enkel een array zijn van drie getallen [XB YB ZB]. Al deze getallen moeten niet-negatief zijn. Standaardwaarde: [0.0 0.0 0.0].
lab.cs.range=De Range entry in Lab color space mag enkel een array zijn van vier getallen [amin amax bmin bmax]. Standaardwaarde: [-100 100 -100 100].
lab.cs.white.point=De WhitePoint array (drie getallen [XW YW ZW]) is verplicht in Lab color space. De getallen XW en ZW moeten positief zijn, en YW moet 1.0 zijn.
last.linebreak.must.be.at.1=laatste lijnafbreking moet op {1}
launch.sound.movie.resetform.importdata.and.javascript.actions.are.not.allowed=Launch, Sound, Movie, ResetForm, ImportData, Hide, SetOCGState, Rendition, Trans, GoTo3DView en JavaScript actions zijn niet toegelaten.
layers.are.not.allowed=Layers zijn niet toegelaten.
layout.out.of.bounds=Layout buiten het grensbereik.
line.iterator.out.of.bounds=lijn iterator buiten het grensbereik.
linear.page.mode.can.only.be.called.with.a.single.parent=Linear page mode kan enkel gebruikt worden met een enkele parent.
lzwdecode.filter.is.not.permitted=LZWDecode filter is niet toegelaten.
lzw.flavour.not.supported=LZW variant niet ondersteund.
macrosegmentid.must.be.gt.eq.0=macroSegmentId moet groter zijn dan 0
macrosegmentid.must.be.lt.macrosemgentcount=macroSegmentId moet kleiner zijn dan macroSemgentCount
macrosemgentcount.must.be.gt.0=macroSemgentCount moet groter zijn dan 0
make.copy.of.catalog.dictionary.is.forbidden=Make copy of Catalog dictionary is forbidden.
mapping.code.should.be.1.or.two.bytes.and.not.1=Mapping code moet uit 1 of 2 bytes bestaan, en niet uit {1}
missing.end.tag=Ontbrekende end tag
missing.endcharmetrics.in.1=Ontbrekende EndCharMetrics in {1}
missing.endfontmetrics.in.1=Ontbrekende EndFontMetrics in {1}
missing.endkernpairs.in.1=Ontbrekende EndKernPairs in {1}
missing.startcharmetrics.in.1=Ontbrekende StartCharMetrics in {1}
missing.tag.s.for.ojpeg.compression=Ontbrekende tag(s) voor OJPEG compressie.
multicolumntext.has.no.columns=MultiColumnText heeft geen kolommen
name.end.tag.out.of.place=Naam end tag op verkeerde plaats.
named.action.type.1.not.allowed=Named action type {1} niet toegelaten.
needappearances.flag.of.the.interactive.form.dictionary.shall.either.not.be.present.or.shall.be.false=NeedAppearances vlag van de interactive form dictionary moet ofwel weggelaten worden, ofwel false zijn.
nested.tags.are.not.allowed=Geneste tags zijn niet toegelaten.
no.compatible.encryption.found=Geen compatibele encryptie gevonden
no.error.just.an.old.style.table=Geen fout, enkel een tabel 'oude stijl'
no.font.is.defined=Er is geen enkele font gedefinieerd.
no.glyphs.defined.for.type3.font=Geen glyphs gedefinieerd voor Type3 font
no.keys.other.than.UR3.and.DocMDP.shall.be.present.in.a.permissions.dictionary=Een permissions dictionary mag geen sleutels bevatten, behalve UR3 en DocMDP.
no.structtreeroot.found=Geen StructTreeRoot gevonden, dit is waarschijnlijk geen tagged PDF document!
no.valid.column.line.found=Geen geldige kolomlijn gevonden.
no.valid.encryption.mode=Geen geldige encryption mode
not.a.placeable.windows.metafile=Geen placeable windows metafile
not.a.valid.jpeg2000.file=Geen geldig Jpeg2000 bestand
not.a.valid.pfm.file=Geen geldig PFM bestand.
not.a.valid.pkcs.7.object.not.a.sequence=Geen geldig PKCS#7 object - geen sequentie
not.a.valid.pkcs.7.object.not.signed.data=Geen geldig PKCS#7 object - niet gesigneerde data
not.all.annotations.could.be.added.to.the.document.the.document.doesn.t.have.enough.pages=Niet alle annotaties konden toegevoegd worden (het document had niet genoeg pagina's).
not.colorized.typed3.fonts.only.accept.mask.images=Kleurloze Type 3 fonts accepteren enkel mask images.
not.identity.crypt.filter.is.not.permitted=Geen Identity Crypt filter is niet toegelaten.
null.outpustream=OutputStream null
number.of.entries.in.this.xref.subsection.not.found=Het aantal entries in deze xref subsectie niet gevonden
object.number.of.the.first.object.in.this.xref.subsection.not.found=Object number van het eerste object in deze xref subsectie niet gevonden
ocsp.status.is.revoked=OCSP status is revoked!
ocsp.status.is.unknown=OCSP Status is onbekend!
only.bmp.can.be.wrapped.in.wmf=Alleen een BMP kan in een WMF gewrapt worden.
only.bmp.png.wmf.gif.and.jpeg.images.are.supported.by.the.rtf.writer=Alleen BMP, PNG, WMF, GIF en JPEG afbeeldingen worden ondersteund door RtfWriter2
only.javascript.actions.are.allowed=Alleen JavaScript acties zijn toegelaten.
only.jpx.baseline.set.of.features.shall.be.used=Alleen de JPX basis set van features mag gebruikt worden.
only.one.of.artbox.or.trimbox.can.exist.in.the.page=Alleen een enkele ArtBox of TrimBox kan bestaan voor een zelfde pagina.
only.pdfa.documents.can.be.added.in.PdfACopy=Enkel PDF/A documenten kunnen toegevoegd worden aan PdfACopy.
only.pdfa.documents.can.be.opened.in.PdfAStamper=Enkel PDF/A documenten kunnen geopend worden met PdfAStamper.
only.pdfa.1.documents.can.be.opened.in.PdfAStamper=Deze instantie van PdfAStamper is geconfigureerd om PDF/A-{1} documenten te verwerken.
only.pdflayer.is.accepted=Alleen PdfLayer is toegelaten.
only.rgb.gray.and.cmyk.are.supported.as.alternative.color.spaces=Alleen RGB, Gray en CMYK worden ondersteund als alternatieve color spaces.
open.actions.by.name.are.not.supported=Open actions by name worden niet ondersteund.
operator.1.already.registered=Operator {1} was al geregistreerd
optional.content.configuration.dictionary.shall.contain.name.entry=Optional content configuratie dictionary moet een Name veld bevatten.
order.array.shall.contain.references.to.all.ocgs=Order array moet referenties naar alle OCGs bevatten.
outputintent.shall.be.prtr.or.mntr=Een PDF/A outputintent profiel moet een output profiel (Device Class = "prtr") of een monitor profiel (Device Class = "mntr") zijn.
outputintent.shall.have.colourspace.gray.rgb.or.cmyk=Een PDF/A outputintent profiel moet een van de colour space "GRAY", "RGB" of "CMYK" hebben.
outputintent.shall.have.gtspdfa1.and.destoutputintent=Een PDF/A OutputIntent dictionary moet GTS_PDFA1 als waarde van zijn S sleutel hebben en een geldige ICC profiel stream als waarde van zijn DestOutputProfile sleutel.
outputintent.shall.not.be.updated=PDF/A OutputIntent mag niet ge�pdatet worden. De original PDF kan device-afhankelijke kleuren bevatten die de huidige OutputIntent nodig hebben.
page.1.invalid.for.segment.2.starting.at.3=page {1} ongeldig voor segment {2} beginnend bij {3}
page.attribute.missing=Ontbrekend pagina attribuut.
page.dictionary.shall.not.include.aa.entry=De page dictionary mag geen AA entry bevatten.
page.dictionary.shall.not.include.pressteps.entry=The page dictionary shall not include a PresSteps entry
page.not.found=Pagina niet gevonden.
page.reordering.requires.a.single.parent.in.the.page.tree.call.pdfwriter.setlinearmode.after.open=Pagina herschikking heeft een enkele parent nodig in de page tree. Gebruik PdfWriter.setLinearMode() na het openen van het document.
page.reordering.requires.an.array.with.the.same.size.as.the.number.of.pages=Pagina herschikking heeft een array nodig met een zelfde aantal elementen als het aantal pagina's.
page.reordering.requires.no.page.repetition.page.1.is.repeated=Pagina herschikking laat geen herhaling van pagina's toe. Pagina {1} werd herhaald.
page.reordering.requires.pages.between.1.and.1.found.2=Pagina herschikking gaat voor pagina's tussen 1 en {1}. Jij vraagt naar pagina {2}.
partial.form.flattening.is.not.supported.with.xfa.forms=Gedeeltelijke form flattening is niet ondersteund voor XFA formulieren.
pdf.array.exceeds.length.set.by.PDFA1.standard=PDF array is groter dan toegelaten door de PDF/A-1 standaard: {1}. Maximumlengte is 8191
pdf.array.is.out.of.bounds=PDF array buiten het grensbereik.
pdf.dictionary.is.out.of.bounds=PDF dictionary buiten het grensbereik.
pdf.header.not.found=PDF header niet gevonden.
pdf.name.is.too.long=PDF name is te lang.
pdf.startxref.not.found=PDF startxref niet gevonden.
pdf.string.is.too.long=PDF string is te lang.
pdfpcells.can.t.have.a.rowspan.gt.1=PdfPCells kunnen geen rowspan groter dan 1 hebben
pdfreader.not.opened.with.owner.password=PdfReader werd niet geopend met het 'owner password'
pdfx.conformance.can.only.be.set.before.opening.the.document=PDFX conformantie kan enkel bepaald worden voor het document geopend wordt.
pdfx.conformance.cannot.be.set.for.PdfAStamperImp.instance=PDFX conformiteit kan niet ingesteld worden voor een PdfAStamperImp instantie.
pdfx.conformance.cannot.be.set.for.PdfAWriter.instance=PDFX conformiteit kan niet ingesteld worden voor een PdfAWriter instantie.
planar.images.are.not.supported=Planar images worden niet ondersteund.
png.filter.unknown=onbekende PNG filter.
postscript.xobjects.are.not.allowed=PostScript XObjects zijn niet toegelaten.
preclose.must.be.called.first=preClose() moet eerst gebeuren.
premature.end.in.1=Vroegtijdig einde van het bestand in {1}
premature.end.of.file=Vroegtijdig einde van het bestand.
premature.eof.while.reading.jpg=Vroegtijdige EOF tijdens het lezen van het JPG bestand.
real.number.is.out.of.range=Re�el getal ligt buiten het bereik.
rebuild.failed.1.original.message.2=Rebuild ging fout: {1}; Oorspronkelijke foutboodschap: {2}
rectanglereadonly.this.rectangle.is.read.only=RectangleReadOnly: dit Rectangle object is alleen-lezen.
reference.pointing.to.reference=Referentie die naar een referentie verwijst.
referring.to.widht.height.of.page.we.havent.seen.yet.1=verwijzing naar een breedte/hoogte van een pagina die we nog niet gezien hebben? {1}
remove.not.supported=remove() niet ondersteund.
reserve.incorrect.column.size=reserve - ongeldige kolom/grootte
resources.do.not.contain.extgstate.entry.unable.to.process.operator.1=De resources bevatten geen ExtGState entry. Het is onmogelijk om de operator {1} te verwerken
root.element.is.not.bookmark.1=Het root element is geen Bookmark: {1}
root.element.is.not.destination=Het root element is geen bestemming.
root.element.is.not.xfdf.1=Het root element is geen xfdf: {1}
rotation.must.be.a.multiple.of.90=De rotatie moet een veelvoud van 90 zijn.
row.coordinate.of.location.must.be.gt.eq.0=rij coordinaat moet groter dan of gelijk aan 0 zijn
scanline.must.begin.with.eol.code.word=Scanline moet beginnen met het EOL code woord.
separations.patterns.and.shadings.are.not.allowed.in.mk.dictionary=Separations, patterns en shadings zijn niet toegelaten in de MK dictionary.
setelement.position.already.taken=setElement - positie al in gebruik
signature.references.dictionary.shall.not.contain.digestlocation.digestmethod.digestvalue=De Signature References dictionary mag de sleutels DigestLocation, DigestMethod and DigestValue niet bevatten.
start.marker.missing.in.1=Start marker ontbreekt in {1}
startxref.is.not.followed.by.a.number=startxref wordt niet gevolgd door een nummer.
startxref.not.found=startxref niet gevonden.
stdcf.not.found.encryption=/StdCF niet gevonden (encryption)
stream.could.not.be.compressed.filter.is.not.a.name.or.array=Stream kon niet gecomprimeerd worden: de filter is geen naam of array.
stream.object.dictionary.shall.not.contain.the.f.ffilter.or.fdecodeparams.keys=Stream object dictionary mag geen F, FFilter of FDecodeParams sleutels bevatten.
structparent.not.found=StructParent niet gevonden.
structparentid.not.found=StructParent ID niet gevonden.
support.only.sha1.hash.algorithm=Enkel ondersteuning voor SHA1 hash algoritme.
support.only.rsa.and.dsa.algorithms=Enkel ondersteuning voor RSA en DSA algoritmes.
invalid.structparent=Ongeldige StructParent.
table.1.does.not.exist.in.2=Tabel '{1}' bestaat niet in {2}
tag.1.not.allowed=Tag {1} niet toegelaten.
tagging.must.be.set.before.opening.the.document=Tagging moet bepaald worden voor het document geopend is.
template.with.tagged.could.not.be.used.more.than.once=Template met tagged content kan niet meermaals gebruikt worden.
text.annotations.should.set.the.nozoom.and.norotate.flag.bits.of.the.f.key.to.1=Text annotations moeten de NoZoom en NoRotate vlaggen van de F sleutel op 1 zetten.
text.cannot.be.null=Text mag niet null zijn.
the.array.must.contain.string.or.pdfannotation=De array moet een String of een PdfAnnotation bevatten.
the.artifact.type.1.is.invalid=Artifact type '{1}' is ongeldig.
the.as.key.shall.not.appear.in.any.optional.content.configuration.dictionary=De AS sleutel mag niet voorkomen in een optional content configuratie dictionary.
the.bit-depth.of.the.jpeg2000.data.shall.have.a.value.in.the.range.1to38=De bit-depth van de JPEG2000 data moet een waarde tussen 1 en 38 hebben.
the.byte.array.is.not.a.recognized.imageformat=De byte array bevat geen gekend afbeeldingsformaat.
the.ccitt.compression.type.must.be.ccittg4.ccittg3.1d.or.ccittg3.2d=Het CCITT compressie type moet CCITTG4, CCITTG3_1D of CCITTG3_2D zijn
the.char.1.doesn.t.belong.in.this.type3.font=Het karakter {1} hoort niet thuis in deze Type3 font
the.char.1.is.not.defined.in.a.type3.font=Het karakter {1} is niet gedefinieerd in de Type3 font
the.character.1.is.illegal.in.codabar=Het karakter '{1}' is ongeldig in codabar.
the.character.1.is.illegal.in.code.39.extended=Het karakter '{1}' is ongeldig in code 39 extended.
the.character.1.is.illegal.in.code.39=Het karakter '{1}' is ongeldig in code 39.
the.cmap.1.does.not.exist.as.a.resource=De cmap {1} bestaat niet als bron.
the.cmap.1.was.not.found=De Cmap {1} werd niet gevonden.
the.compression.1.is.not.supported=Compressie {1} wordt niet ondersteund.
the.document.catalog.dictionary.shall.contain.metadata=De document catalog dictionary van een PDF/A conform document moet de Metadata sleutel bevatten.
the.document.catalog.dictionary.shall.not.include.an.aa.entry=De document catalog dictionary mag geen AA veld bevatten.
the.document.catalog.dictionary.shall.not.include.alternatepresentation.names.entry=De document catalog dictionary mag geen AlternatePresentation entry bevatten in de Names entry.
the.document.catalog.dictionary.shall.not.include.a.needrendering.entry=De document catalog dictionary mag geen NeedRendering entry bevatten.
the.document.catalog.dictionary.shall.not.include.a.requirements.entry=De document catalog dictionary mag geen Requirements entry bevatten.
the.document.catalog.dictionary.shall.not.include.acroform.xfa.entry=De document catalog dictionary mag geen XFA entry bvatten in de AcroForm entry.
the.document.catalog.dictionary.shall.not.include.embeddedfiles.names.entry=De document catalog dictionary mag geen EmbeddedFiles entry bevatten in de Names entry.
the.document.does.not.contain.parenttree=Het document bevat geen ParentTree.
the.document.has.been.closed.you.can.t.add.any.elements=Het document is gesloten. Je kan geen elementen meer toevoegen.
the.document.has.no.catalog.object=Het document heeft geen catalog object (dit betekent: het is een ongeldige PDF).
the.document.has.no.page.root=Het document heeft geen page root (dit betekent: het is een ongeldige PDF).
the.document.has.no.pages=Het document heeft geen pagina's.
the.document.is.not.open.yet.you.can.only.add.meta.information=Het document is nog niet open; je kan enkel metagegevens toevoegen.
the.document.is.not.open=Het document is niet open.
the.document.is.open.you.can.only.add.elements.with.content=Het document is open; je kan enkel Element objecten met inhoud plaatsen.
the.document.must.be.open.to.import.rtf.documents=Het document moet geopend zijn om RTF documenten te kunnen importeren.
the.document.must.be.open.to.import.rtf.fragments=Het document moet geopend zijn om RTF fragmenten te kunnen importeren.
the.document.was.reused=Het document werd herbruikt.
the.export.and.the.display.array.must.have.the.same.size=De export en display array moeten uit een zelfde aantal elementen bestaan.
the.f.keys.print.flag.bit.shall.be.set.to.1.and.its.hidden.invisible.and.noview.flag.bits.shall.be.set.to.0=De Print vlag van de F sleutel moet op 1 gezet worden en de Hidden, Invisible en NoView vlaggen moeten op 0 gezet worden.
the.f.keys.print.flag.bit.shall.be.set.to.1.and.its.hidden.invisible.noview.and.togglenoview.flag.bits.shall.be.set.to.0=De Print vlag van de F sleutel moet op 1 gezet worden en de Hidden, Invisible, NoView en ToggleNoView vlaggen moeten op 0 gezet worden.
the.field.1.already.exists=Het veld {1} bestaat al.
the.field.1.does.not.exist=Het veld {1} bestaat niet.
the.field.1.is.not.a.signature.field=Het veld {1} is geen signature veld.
the.file.does.not.contain.any.valid.image=Het bestand bevat geen geldige afbeelding.
the.filter.1.is.not.supported=De filter {1} wordt niet ondersteund.
the.font.index.for.1.must.be.between.0.and.2.it.was.3=De font index voor {1} moet tussen 0 en {2} liggen. Het was {3}.
the.font.index.for.1.must.be.positive=De font index voor {1} moet positief zijn.
the.image.mask.is.not.a.mask.did.you.do.makemask=De image mask is geen mask. Heb je makeMask() gebruikt?
the.image.must.have.absolute.positioning=Het image object moet een absolute positie hebben.
the.key.1.didn.t.reserve.space.in.preclose=De sleutel {1} reserveerde geen plaats in preClose().
the.key.1.is.too.big.is.2.reserved.3=De sleutel {1} is te lang. Hij is {2}, terwijl er maar {3} gereserveerd werd
the.layer.1.already.has.a.parent=De layer '{1}' heeft al een parent.
the.matrix.size.must.be.6=De matrix moet uit 6 elementen bestaan.
the.name.1.is.too.long.2.characters=De naam '{1}' is te lang ({2} karakters).
the.new.size.must.be.positive.and.lt.eq.of.the.current.size=De nieuwe waarde moet positief zijn en kleiner dan of gelijk aan de huidige waarde
the.number.of.booleans.in.this.array.doesn.t.correspond.with.the.number.of.fields=Het aantal booleans in deze array stemt niet overeen met het aantal velden.
the.number.of.columns.in.pdfptable.constructor.must.be.greater.than.zero=Het aantal kolommen in de PdfPTable constructor moet groter zijn dan nul.
the.number.of.colour.channels.in.the.jpeg2000.data.shall.be.123=Het aantal colour channels in de JPEG2000 data moet 1, 2 of 3 zijn.
the.original.document.was.reused.read.it.again.from.file=Het originele document werd herbruikt. Lees het opnieuw in van het bestand.
the.page.less.3.units.nor.greater.14400.in.either.direction=De grootte van elke page boundary mag niet kleiner zijn dan 3 eenheden in elke richting en mag niet groter zijn dan 14400 eenheden in elke richting.
the.page.number.must.be.gt.eq.1=Het paginanummer moet groter dan of gelijk zijn aan 1.
the.page.size.must.be.smaller.than.14400.by.14400.its.1.by.2=De pagina grote moet kleiner zijn dan 14400 bij 14400. Het is {1} bij {2}.
the.parent.has.already.another.function=De parent heeft al een andere functie.
the.photometric.1.is.not.supported=De photometric {1} wordt niet ondersteund.
the.resource.cjkencodings.properties.does.not.contain.the.encoding.1=Het bronbestand cjkencodings.properties bevat de encoding {1} niet
the.smask.key.is.not.allowed.in.extgstate=De SMask sleutel is niet toegelaten in ExtGState.
the.smask.key.is.not.allowed.in.images=De /SMask key is niet toegelaten in afbeeldingen.
the.spot.color.must.be.the.same.only.the.tint.can.vary=De spot color moet het zelfde zijn, alleen de tint mag varieren.
the.stack.is.empty=De stack is leeg.
the.structure.has.kids=De structuur heeft substructuren (kids).
the.table.width.must.be.greater.than.zero=De breedte van de tabel moet groter zijn dan nul.
the.template.can.not.be.null=De template mag niet null zijn.
the.text.is.too.big=De tekst is te lang.
the.text.length.must.be.even=de lengte van de tekst mag niet oneven zijn.
the.two.barcodes.must.be.composed.externally=De twee barcodes moet extern samengesteld worden.
the.update.dictionary.has.less.keys.than.required=De update dictionary heeft minder sleutels dan noodzakelijk is.
the.url.of.the.image.is.missing=De URL van de afbeelding ontbreekt.
the.value.has.to.be.true.of.false.instead.of.1=De waarde moet 'true' of 'false' zijn, in plaats van '{1}'.
the.value.of.interpolate.key.shall.not.be.true=De waarde van de Interpolate sleutel mag niet true zijn.
the.value.of.the.meth.entry.in.colr.box.shall.be.123=De waarde van het METH veld in 'colr' box moet 1, 2 of 3 zijn.
the.width.cannot.be.set=De breedte kan niet gedefinieerd worden.
the.widths.array.in.pdfptable.constructor.can.not.be.null=De array met de breedtes in de PdfPTable constructor kan niet null zijn.
the.widths.array.in.pdfptable.constructor.can.not.have.zero.length=De array met de breedtes in de PdfPTable constructor moet minstens een element hebben.
the.writer.in.pdfcontentbyte.is.null=De writer in PdfContentByte is null.
there.are.illegal.characters.for.barcode.128.in.1=Er zitten ongeldige karakters voor barcode 128 in '{1}'.
there.are.not.enough.imported.pages.for.copied.fields=Er zijn niet genoeg imported pages voor de gekopieerde fields. Gebruik addDocument of kopieer genoeg pagina's.
this.acrofields.instance.is.read.only=Deze instantie van AcroFields is read only.
this.image.can.not.be.an.image.mask=Deze afbeelding kan geen image mask zijn.
this.largeelement.has.already.been.added.to.the.document=Dit LargeElement object werd al toegevoegd aan het Document.
this.page.cannot.be.replaced.new.content.was.already.added=Deze pagina kan niet vervangen worden: nieuwe inhoud werd al toegevoegd
this.pkcs.7.object.has.multiple.signerinfos.only.one.is.supported.at.this.time=Dit PKCS#7 object heeft meerdere SignerInfos - slechts een enkel is toegelaten
tiff.5.0.style.lzw.codes.are.not.supported=TIFF 5.0-achtige LZW codes worden niet ondersteund.
tiff.fill.order.tag.must.be.either.1.or.2=TIFF_FILL_ORDER tag moet ofwel 1, ofwel 2 zijn.
tiles.are.not.supported=Tiles worden niet ondersteund.
title.cannot.be.null=De titel kan niet null zijn.
token.obj.expected=Token 'obj' verwacht.
too.many.indirect.objects=Teveel indirect objects.
trailer.not.found=trailer niet gevonden.
trailer.prev.entry.points.to.its.own.cross.reference.section=Trailer /Prev veld verwijst naar zijn eigen cross-reference sectie.
transparency.is.not.allowed.ca.eq.1=Transparentie is niet toegelaten: /ca = {1}
transparency.length.must.be.equal.to.2.with.ccitt.images=De transparency lengte moet gelijk zijn aan 2 voor CCITT afbeeldingen.
transparency.length.must.be.equal.to.componentes.2=De transparentie lengte moet gelijk zijn aan (components * 2)
trying.to.create.a.table.without.rows=Je probeert een tabel zonder rijen te maken.
tsa.1.failed.to.return.time.stamp.token.2=TSA '{1}' slaagde er niet in een time stamp token terug te geven: {2}
two.byte.arrays.are.needed.if.the.type1.font.is.embedded=Er zijn twee byte arrays nodig als de Type1 font ingebed wordt.
type3.font.used.with.the.wrong.pdfwriter=Type3 font gebruikt met het verkeerde PdfWriter object
types.is.null=types is null
unbalanced.begin.end.marked.content.operators=Ongebalanceerde begin/einde operatoren voor marked content.
unbalanced.begin.end.text.operators=Ongebalanceerde begin/einde operatoren voor tekst.
path.construction.operator.inside.text.object=Pad constructie of drawing operators aren't allowed inside a text object.
unbalanced.layer.operators=Ongebalanceerde operatoren voor layers.
unbalanced.marked.content.operators=Ongebalanceerde marked content operatoren.
unbalanced.save.restore.state.operators=Ongebalanceerde save/restore state operatoren.
unexpected.color.space.in.embedded.icc.profile=Onverwachte kleurenruimte in ingesloten ICC-profiel. Het zal worden genegeerd.
unexpected.end.of.file=Onverwacht einde van het bestand.
unexpected.eof=Onverwachte EOF
unexpected.gt.gt=Onverwachte '>>'
unexpected.close.bracket=Onverwachter ']'
unknown.color.format.must.be.rgb.or.rrggbb=Onbekend formaat voor kleur. Moet #RGB of #RRGGBB zijn
unknown.encryption.type.r.eq.1=Onbekend encryptie type R = {1}
unknown.encryption.type.v.eq.1=Onbekend encryptie type V = {1}
unknown.filter.1=Onbekende filter: {1}
unknown.hash.algorithm.1=Onbekend Hash Algoritme {1}
unknown.image.format={1} is geen erkend afbeeldingsformaat.
unknown.key.algorithm.1=Onbekend Key Algoritme {1}
unknown.object.at.k.1=Onbekend object voor /K {1}
unknown.structure.element.role.1=Onbekende structure element role: {1}.
unknown=onbekend
unsupported.box.size.eq.eq.0=Ongeldige box size == 0
unsupported.in.this.context.use.pdfstamper.addannotation=Niet ondersteund in deze context. Gebruik PdfStamper.addAnnotation()
use.pdfstamper.getundercontent.or.pdfstamper.getovercontent=Gebruik PdfStamper.getUnderContent() of PdfStamper.getOverContent()
use.pdfstamper.setthumbnail=Gebruik PdfStamper.setThumbnail().
use.setpageaction.pdfname.actiontype.pdfaction.action.int.page=Gebruik setPageAction(PdfName actionType, PdfAction action, int page)
userunit.should.be.a.value.between.1.and.75000=UserUnit moet een waarde zijn tussen 1 en 75000.
value.of.name.entry.shall.be.unique.amongst.all.optional.content.configuration.dictionaries=De waarde van het Name veld moet uniek zijn over alle optional content configuratie dictionaries
verification.already.output=Verificatie reeds weggeschreven.
verticaltext.go.with.simulate.eq.eq.false.and.text.eq.eq.null=VerticalText.go met simulate==false en text==null.
while.removing.wmf.placeable.header=tijdens het verwijderen van een wmf positioneerbare header
widget.annotation.dictionary.or.field.dictionary.shall.not.include.a.or.aa.entry=Widget annotation dictionary of Field dictionary mogen geen A of AA veld bevatten.
writelength.can.only.be.called.after.output.of.the.stream.body=writeLength() kan alleen opgeroepen worden na output van de stream body.
writelength.can.only.be.called.in.a.contructed.pdfstream.inputstream.pdfwriter=writeLength() kan alleen opgeroepen worden in een aldus aangemaakte stream: PdfStream(InputStream,PdfWriter).
wrong.number.of.columns=Verkeerd aantal kolommen.
xmllocator.cannot.be.null=XmlLocator mag niet null zijn, zie XmlSignatureAppearance.
XObject.1.is.not.a.stream=XObject {1} is geen stream.
xref.subsection.not.found=xref subsectie niet gevonden
xstep.or.ystep.can.not.be.zero=XStep of YStep kan niet nul zijn.
you.can.only.add.a.writer.to.a.pdfdocument.once=Je kan een writer slecht een enkele keer toevoegen aan een PdfDocument.
you.can.only.add.cells.to.rows.no.objects.of.type.1=Je kan enkel cellen toevoegen aan rijen, geen objecten van type {1}
you.can.only.add.objects.that.implement.the.element.interface=Enkel objecten die de Element interface implementeren kunnen toegevoegd worden.
you.can.t.add.a.1.to.a.section=Je kan geen {1} toevoegen aan een Section.
you.can.t.add.an.element.of.type.1.to.a.simplecell=Je kan geen element van type {1} toevoegen aan een SimpleCell.
you.can.t.add.cells.to.a.table.directly.add.them.to.a.row.first=Je kan geen cellen toevoegen aan een tabel, zonder ze eerst aan een rij toe te voegen.
you.can.t.add.listitems.rows.or.cells.to.a.cell=Je kan geen lijst items, rijen of cellen toevoegen aan een cell.
you.can.t.add.one.row.to.another.row=Je kan rijen niet aan rijen toevoegen.
you.can.t.have.1.pages.on.one.page.minimum.2.maximum.8=Je kan geen {1} pagina's op ��n blad plaatsen (minimum 2; maximum 8).
you.can.t.set.the.full.compression.if.the.document.is.already.open=Je mag geen full compression invoeren als het document al open is.
you.can.t.set.the.initial.leading.if.the.document.is.already.open=Je kan geen initial leading bepalen als het document al open is.
you.can.t.split.this.document.at.page.1.there.is.no.such.page=Je kan dit document niet splitsen op pagina {1}; Die pagina bestaat niet.
you.can.t.translate.a.negative.number.into.an.alphabetical.value=Je kan een negatieve waarde niet omzetten naar een alfabetische waarde.
you.have.to.consolidate.the.named.destinations.of.your.reader=Je moet de named destinations van je reader object consolideren.
you.have.to.define.a.boolean.array.for.this.collection.sort.dictionary=Je moet een boolean array bepalen voor deze collection sort dictionary.
you.have.used.the.wrong.constructor.for.this.fieldpositioningevents.class=Je gebruikte de verkeerde constructor van de FieldPositioningEvents class.
you.must.set.a.value.before.adding.a.prefix=Je moet een waarde toevoegen vooraleer je een prefix toevoegt.
you.need.a.single.boolean.for.this.collection.sort.dictionary=Je hebt een enkele boolean nodig voor deze collection sort dictionary.
the.decode.parameter.type.1.is.not.supported=De decodeer parameter type {1} is niet ondersteund.
the.color.space.1.is.not.supported=De color space {1} is niet ondersteund.
the.color.depth.1.is.not.supported=De kleurdiepte {1} is niet ondersteund.
N.value.1.is.not.supported=N waarde {1} is niet ondersteund
Decoding.can't.happen.on.this.type.of.stream.(.1.)=Decoderen kan niet gebeuren op dit type stream({1})
zugferd.xmp.schema.shall.contain.attachment.name=ZUGFeRD XMP schema moet attachmentnaam bevatten.
invalid.reference.number.skip=Invalid reference was found. Reference will be skipped.
